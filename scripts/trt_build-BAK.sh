#!/usr/bin/env bash
set -euo pipefail

###############################################################################
# BASIC CONFIG
###############################################################################
MODEL_ID="nvidia/Llama-3.1-8B-Instruct-FP8"
REPO_ROOT="/models"                  # this is the triton_repo volume mount
MODEL_NAME="llama3"
CKPT_DIR="${REPO_ROOT}/llama3_ckpt"  # tmp checkpoint
ENGINE_DIR="${REPO_ROOT}/${MODEL_NAME}/1"   # ★ Triton-style hierarchy
SNAPSHOT_DIR="/tmp/hf/${MODEL_ID//\//_}"
###############################################################################

export MODEL_ID SNAPSHOT_DIR CKPT_DIR ENGINE_DIR REPO_ROOT MODEL_NAME

echo "📥  Downloading $MODEL_ID to $SNAPSHOT_DIR …"
python - <<'PY'
from huggingface_hub import snapshot_download
import os
snapshot_download(
    repo_id=os.environ["MODEL_ID"],
    local_dir=os.environ["SNAPSHOT_DIR"],
    token=os.getenv("HUGGING_FACE_HUB_TOKEN"),
)
PY

mkdir -p "${CKPT_DIR}"

echo "🔧  Converting HF weights → TRT-LLM checkpoint …"
python - <<'PY'
import os
from tensorrt_llm.models import LLaMAForCausalLM            # ✅ keep model
from tensorrt_llm.mapping import Mapping                    # ✅ correct import

llama = LLaMAForCausalLM.from_hugging_face(
    os.environ["SNAPSHOT_DIR"],
    dtype="float16",
    mapping=Mapping(tp_size=1, pp_size=1, world_size=1, rank=0),
)
llama.save_checkpoint(os.environ["CKPT_DIR"], save_config=True)
PY

echo "⚙️   Building TensorRT engine …"
mkdir -p "${ENGINE_DIR}"
/usr/local/bin/trtllm-build \
    --checkpoint_dir "${CKPT_DIR}" \
    --output_dir     "${ENGINE_DIR}"

echo "📝  Generating Triton config …"
python -m tensorrt_llm.tools.trtllm_config \
  --engine_dir    "${ENGINE_DIR}" \
  --output_dir    "${REPO_ROOT}/${MODEL_NAME}" \
  --model_name    "${MODEL_NAME}" \
  --dtype         fp8 \
  --max_batch_size 128 \
  --max_context_length 4096

echo "🟢  Build & repo generation complete!"
