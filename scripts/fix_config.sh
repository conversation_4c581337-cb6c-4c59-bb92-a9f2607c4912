#!/bin/bash

# Fix the config.pbtxt file for TensorRT-LLM backend
echo "🔧 Fixing config.pbtxt for TensorRT-LLM backend..."

cat > /models/llama3/config.pbtxt << 'EOF'
name: "llama3"
backend: "tensorrtllm"
max_batch_size: 8

model_transaction_policy {
  decoupled: false
}

instance_group [
  { kind: KIND_GPU }
]

input [
  { name: "input_ids", data_type: TYPE_INT32, dims: [ -1 ] }
]

output [
  { name: "output_ids", data_type: TYPE_INT32, dims: [ -1 ] }
]

parameters {
  key: "gpt_model_type"
  value: { string_value: "inflight_fused_batching" }
}

parameters {
  key: "gpt_model_path"
  value: { string_value: "/models/llama3/1" }
}

parameters {
  key: "engine_dir"
  value: { string_value: "1" }
}

parameters {
  key: "decoupled"
  value: { string_value: "false" }
}

parameters {
  key: "max_beam_width"
  value: { string_value: "1" }
}

parameters {
  key: "batch_scheduler_policy"
  value: { string_value: "max_utilization" }
}

parameters {
  key: "kv_cache_free_gpu_mem_fraction"
  value: { string_value: "0.5" }
}

parameters {
  key: "exclude_input_in_output"
  value: { string_value: "true" }
}
EOF

echo "✅ Config.pbtxt updated with TensorRT-LLM backend parameters"
echo "📋 New config preview:"
head -10 /models/llama3/config.pbtxt
