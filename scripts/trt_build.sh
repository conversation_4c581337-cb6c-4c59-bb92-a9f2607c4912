#!/usr/bin/env bash
set -euo pipefail

###############################################################################
# BASIC CONFIG
###############################################################################
MODEL_ID="meta-llama/Meta-Llama-3-8B-Instruct"
MODEL_NAME="llama3"
REPO_ROOT="/models"                 # <-- Triton model repository (volume)

TP_SIZE=1
PP_SIZE=1
MAX_BATCH=8
DTYPE="float16"                     # fp16 / bf16

###############################################################################
# DERIVED PATHS  (keep CKPT_DIR **OUTSIDE** REPO_ROOT!)
###############################################################################
CKPT_DIR="/workspace/${MODEL_NAME}_ckpt"  # Changed from inside REPO_ROOT to workspace
ENGINE_DIR="${REPO_ROOT}/${MODEL_NAME}/1"
SNAPSHOT_DIR="/tmp/hf/${MODEL_ID//\//_}"

export MODEL_ID MODEL_NAME REPO_ROOT TP_SIZE PP_SIZE MAX_BATCH DTYPE \
       CKPT_DIR ENGINE_DIR SNAPSHOT_DIR

###############################################################################
# 0.  Faster HF download
###############################################################################
python -m pip install --no-cache-dir --upgrade "huggingface_hub[hf_xet]" >/dev/null 2>&1

###############################################################################
# 1.  Download model
###############################################################################
echo "📥  Downloading »${MODEL_ID}« …"
python - <<'PY'
from huggingface_hub import snapshot_download
import os
snapshot_download(
    repo_id=os.environ["MODEL_ID"],
    local_dir=os.environ["SNAPSHOT_DIR"],
    token=os.getenv("HUGGING_FACE_HUB_TOKEN"),
)
PY
echo "✅  Download complete."

###############################################################################
# 2.  HF → TRT-LLM checkpoint
###############################################################################
mkdir -p "${CKPT_DIR}"
echo "🔧  Converting HF weights → TRT-LLM checkpoint …"
python - <<'PY'
import os
from tensorrt_llm.models import LLaMAForCausalLM
from tensorrt_llm.mapping import Mapping

llama = LLaMAForCausalLM.from_hugging_face(
    os.environ["SNAPSHOT_DIR"],                # positional arg
    dtype=os.environ["DTYPE"],
    mapping=Mapping(
        tp_size=int(os.environ["TP_SIZE"]),
        pp_size=int(os.environ["PP_SIZE"]),
        world_size=1,
        rank=0,
    ),
)
llama.save_checkpoint(os.environ["CKPT_DIR"], save_config=True)
PY
echo "✅  Checkpoint saved."

###############################################################################
# 3.  Build engine
###############################################################################
mkdir -p "${ENGINE_DIR}"
echo "⚙️   Building TensorRT engine …"
/usr/local/bin/trtllm-build \
      --checkpoint_dir "${CKPT_DIR}" \
      --output_dir     "${ENGINE_DIR}" \
      --max_batch_size "${MAX_BATCH}"
echo "✅  Engine build finished."

###############################################################################
# 4.  Copy tokenizer files (for future text-level pipelines)
###############################################################################
cp -f "${SNAPSHOT_DIR}"/tokenizer* "${REPO_ROOT}/${MODEL_NAME}/" 2>/dev/null || true

###############################################################################
# 5.  Write config.pbtxt
###############################################################################
echo "📝  Writing config.pbtxt …"
python - <<'PY'
import os, textwrap, pathlib
root = pathlib.Path(os.environ["REPO_ROOT"])
m    = os.environ["MODEL_NAME"]

cfg = textwrap.dedent(f"""
name: "{m}"
backend: "tensorrt_llm"
max_batch_size: {os.environ['MAX_BATCH']}

instance_group [
  {{ kind: KIND_GPU }}
]

input [
  {{ name: "input_ids"          data_type: TYPE_INT32 dims: [ -1 ] }}
]

output [
  {{ name: "output_ids" data_type: TYPE_INT32 dims: [ -1 ] }}
]

parameters {{
  key: "model_type"
  value: {{ string_value: "llama" }}
}}

parameters {{
  key: "engine_dir"
  value: {{ string_value: "1" }}
}}

parameters {{
  key: "decoupled"
  value: {{ string_value: "False" }}
}}

parameters {{
  key: "max_beam_width"
  value: {{ string_value: "1" }}
}}

parameters {{
  key: "batch_scheduler_policy"
  value: {{ string_value: "max_utilization" }}
}}
""")

(root / m).mkdir(parents=True, exist_ok=True)
with open(root / m / "config.pbtxt", "w") as f:
    f.write(cfg)
PY

###############################################################################
# 6.  Rename engine file if needed
###############################################################################
echo "🔄  Renaming engine file if needed..."
if [ -f "${ENGINE_DIR}/rank0.engine" ] && [ ! -f "${ENGINE_DIR}/model.plan" ]; then
    mv "${ENGINE_DIR}/rank0.engine" "${ENGINE_DIR}/model.plan"
    echo "✅  Engine file renamed to model.plan"
fi

# Add a verification step to check if the model files exist
echo "🔍  Verifying model files..."
if [ -f "${ENGINE_DIR}/model.plan" ]; then
    echo "✅  Engine file exists at ${ENGINE_DIR}/model.plan"
else
    echo "❌  ERROR: Engine file not found at ${ENGINE_DIR}/model.plan"
    ls -la "${ENGINE_DIR}"
fi

if [ -f "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt" ]; then
    echo "✅  Config file exists at ${REPO_ROOT}/${MODEL_NAME}/config.pbtxt"
else
    echo "❌  ERROR: Config file not found at ${REPO_ROOT}/${MODEL_NAME}/config.pbtxt"
fi

echo "🟢  Build & Triton repo generation complete!"
