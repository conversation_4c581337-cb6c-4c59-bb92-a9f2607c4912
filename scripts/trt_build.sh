#!/usr/bin/env bash
set -euo pipefail

###############################################################################
# Install TensorRT-LLM 0.17.0 for compatibility with Triton 25.02
###############################################################################
echo "🔧  Installing TensorRT-LLM 0.17.0 for compatibility with Triton 25.02..."
pip install --extra-index-url https://pypi.nvidia.com tensorrt-llm==0.17.0
echo "✅  TensorRT-LLM 0.17.0 installed successfully."

###############################################################################
# BASIC CONFIG
###############################################################################
MODEL_ID="meta-llama/Meta-Llama-3-8B-Instruct"
MODEL_NAME="llama3"
REPO_ROOT="/models"                 # <-- Triton model repository (volume)

TP_SIZE=1
PP_SIZE=1
MAX_BATCH=8
DTYPE="float16"                     # fp16 / bf16

###############################################################################
# DERIVED PATHS  (keep CKPT_DIR **OUTSIDE** REPO_ROOT!)
###############################################################################
CKPT_DIR="/workspace/${MODEL_NAME}_ckpt"  # Changed from inside REPO_ROOT to workspace
ENGINE_DIR="${REPO_ROOT}/${MODEL_NAME}/1"
SNAPSHOT_DIR="/tmp/hf/${MODEL_ID//\//_}"

export MODEL_ID MODEL_NAME REPO_ROOT TP_SIZE PP_SIZE MAX_BATCH DTYPE \
       CKPT_DIR ENGINE_DIR SNAPSHOT_DIR

###############################################################################
# 0.  Faster HF download
###############################################################################
python -m pip install --no-cache-dir --upgrade "huggingface_hub[hf_xet]" >/dev/null 2>&1

###############################################################################
# 1.  Download model
###############################################################################
echo "📥  Downloading »${MODEL_ID}« …"
python - <<'PY'
from huggingface_hub import snapshot_download
import os
snapshot_download(
    repo_id=os.environ["MODEL_ID"],
    local_dir=os.environ["SNAPSHOT_DIR"],
    token=os.getenv("HUGGING_FACE_HUB_TOKEN"),
)
PY
echo "✅  Download complete."

###############################################################################
# 2.  HF → TRT-LLM checkpoint
###############################################################################
mkdir -p "${CKPT_DIR}"
echo "🔧  Converting HF weights → TRT-LLM checkpoint …"
python - <<'PY'
import os
from tensorrt_llm.models import LLaMAForCausalLM
from tensorrt_llm.mapping import Mapping

llama = LLaMAForCausalLM.from_hugging_face(
    os.environ["SNAPSHOT_DIR"],                # positional arg
    dtype=os.environ["DTYPE"],
    mapping=Mapping(
        tp_size=int(os.environ["TP_SIZE"]),
        pp_size=int(os.environ["PP_SIZE"]),
        world_size=1,
        rank=0,
    ),
)
llama.save_checkpoint(os.environ["CKPT_DIR"], save_config=True)
PY
echo "✅  Checkpoint saved."

###############################################################################
# 3.  Build engine
###############################################################################
mkdir -p "${ENGINE_DIR}"
echo "⚙️   Building TensorRT engine …"
/usr/local/bin/trtllm-build \
      --checkpoint_dir "${CKPT_DIR}" \
      --output_dir     "${ENGINE_DIR}" \
      --max_batch_size "${MAX_BATCH}"
echo "✅  Engine build finished."

###############################################################################
# 4.  Copy tokenizer files (for future text-level pipelines)
###############################################################################
cp -f "${SNAPSHOT_DIR}"/tokenizer* "${REPO_ROOT}/${MODEL_NAME}/" 2>/dev/null || true

###############################################################################
# 5.  Generate Triton config using official TensorRT-LLM tool
###############################################################################
echo "📝  Generating Triton config using official tool …"
if python -m tensorrt_llm.tools.trtllm_config \
  --engine_dir    "${ENGINE_DIR}" \
  --output_dir    "${REPO_ROOT}/${MODEL_NAME}" \
  --model_name    "${MODEL_NAME}" \
  --dtype         "${DTYPE}" \
  --max_batch_size "${MAX_BATCH}" \
  --max_context_length 4096; then
    echo "✅  Triton config generated successfully."
else
    echo "❌  ERROR: Failed to generate Triton config. Trying fallback method..."
    # Fallback: create a basic config manually
    mkdir -p "${REPO_ROOT}/${MODEL_NAME}"
    cat > "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt" << EOF
name: "${MODEL_NAME}"
backend: "tensorrtllm"
max_batch_size: ${MAX_BATCH}

model_transaction_policy {
  decoupled: false
}

instance_group [
  { kind: KIND_GPU }
]

input [
  { name: "input_ids", data_type: TYPE_INT32, dims: [ -1 ] }
]

output [
  { name: "output_ids", data_type: TYPE_INT32, dims: [ -1 ] }
]

parameters {
  key: "gpt_model_type"
  value: { string_value: "inflight_fused_batching" }
}

parameters {
  key: "gpt_model_path"
  value: { string_value: "/models/${MODEL_NAME}/1" }
}

parameters {
  key: "engine_dir"
  value: { string_value: "1" }
}

parameters {
  key: "decoupled"
  value: { string_value: "false" }
}

parameters {
  key: "max_beam_width"
  value: { string_value: "1" }
}

parameters {
  key: "batch_scheduler_policy"
  value: { string_value: "max_utilization" }
}

parameters {
  key: "kv_cache_free_gpu_mem_fraction"
  value: { string_value: "0.5" }
}

parameters {
  key: "exclude_input_in_output"
  value: { string_value: "true" }
}
EOF
    echo "✅  Fallback config created."
fi

###############################################################################
# 6.  Keep engine file as rank0.engine (required by TensorRT-LLM backend)
###############################################################################
echo "🔄  Ensuring engine file is named correctly..."
if [ -f "${ENGINE_DIR}/model.plan" ] && [ ! -f "${ENGINE_DIR}/rank0.engine" ]; then
    mv "${ENGINE_DIR}/model.plan" "${ENGINE_DIR}/rank0.engine"
    echo "✅  Engine file renamed to rank0.engine"
elif [ -f "${ENGINE_DIR}/rank0.engine" ]; then
    echo "✅  Engine file already correctly named as rank0.engine"
fi

# Add comprehensive verification step
echo "🔍  Verifying model files and structure..."

# Check engine file
if [ -f "${ENGINE_DIR}/rank0.engine" ]; then
    echo "✅  Engine file exists at ${ENGINE_DIR}/rank0.engine"
    echo "    Size: $(du -h "${ENGINE_DIR}/rank0.engine" | cut -f1)"
else
    echo "❌  ERROR: Engine file not found at ${ENGINE_DIR}/rank0.engine"
    echo "    Contents of ${ENGINE_DIR}:"
    ls -la "${ENGINE_DIR}" || echo "    Directory does not exist"
fi

# Check config file
if [ -f "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt" ]; then
    echo "✅  Config file exists at ${REPO_ROOT}/${MODEL_NAME}/config.pbtxt"
    echo "    Preview of config:"
    head -10 "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt" | sed 's/^/    /'
else
    echo "❌  ERROR: Config file not found at ${REPO_ROOT}/${MODEL_NAME}/config.pbtxt"
    echo "    Contents of ${REPO_ROOT}/${MODEL_NAME}:"
    ls -la "${REPO_ROOT}/${MODEL_NAME}" || echo "    Directory does not exist"
fi

# Check overall model directory structure
echo "📁  Model repository structure:"
find "${REPO_ROOT}/${MODEL_NAME}" -type f -exec ls -lh {} \; 2>/dev/null | sed 's/^/    /' || echo "    No files found"

# Test file permissions
echo "🔐  Checking file permissions..."
if [ -f "${ENGINE_DIR}/rank0.engine" ]; then
    echo "    Engine file permissions: $(stat -c '%A %U:%G' "${ENGINE_DIR}/rank0.engine")"
fi
if [ -f "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt" ]; then
    echo "    Config file permissions: $(stat -c '%A %U:%G' "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt")"
fi

echo "🟢  Build & Triton repo generation complete!"

###############################################################################
# 7.  Final validation - Test model repository structure
###############################################################################
echo "🧪  Running final validation..."

# Validate the model repository structure for Triton
echo "📋  Triton model repository validation:"
echo "    Expected structure: ${REPO_ROOT}/${MODEL_NAME}/"
echo "    ├── config.pbtxt"
echo "    └── 1/"
echo "        └── rank0.engine"
echo ""

# Check if the structure matches Triton expectations
VALIDATION_PASSED=true

if [ ! -d "${REPO_ROOT}/${MODEL_NAME}" ]; then
    echo "❌  Model directory missing: ${REPO_ROOT}/${MODEL_NAME}"
    VALIDATION_PASSED=false
fi

if [ ! -f "${REPO_ROOT}/${MODEL_NAME}/config.pbtxt" ]; then
    echo "❌  Config file missing: ${REPO_ROOT}/${MODEL_NAME}/config.pbtxt"
    VALIDATION_PASSED=false
fi

if [ ! -d "${ENGINE_DIR}" ]; then
    echo "❌  Version directory missing: ${ENGINE_DIR}"
    VALIDATION_PASSED=false
fi

if [ ! -f "${ENGINE_DIR}/rank0.engine" ]; then
    echo "❌  Engine file missing: ${ENGINE_DIR}/rank0.engine"
    VALIDATION_PASSED=false
fi

if [ "$VALIDATION_PASSED" = true ]; then
    echo "✅  All validation checks passed!"
    echo "🚀  Model repository is ready for Triton server!"
    echo ""
    echo "📝  Next steps:"
    echo "    1. Start Triton server: docker compose up -d triton --no-deps"
    echo "    2. Check health: curl -s http://localhost:8000/v2/health/ready"
    echo "    3. List models: curl -s http://localhost:8000/v2/models"
else
    echo "❌  Validation failed! Please check the errors above."
    exit 1
fi
