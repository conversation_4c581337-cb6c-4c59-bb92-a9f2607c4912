# If using Huggingface, ALWAYS go to the Huggingface model page that you plan to download using the account
    that you will download the model with and accept the terms and get permission to download or it will FAIL.

# Make sure to give the script permission to run after EVERY modification.
chmod +x ~/stacks/nv-aistack/scripts/trt_build.sh

# To fire up the build do the following
docker compose rm -f trt-builder
docker compose up -d --force-recreate --no-deps trt-builder

# Watch the logs while it downloads and builds
docker compose logs -f trt-builder

# You may need to rename the rank0.engine to model.plan
sudo mv /data/models/triton_repo/llama3/1/rank0.engine /data/models/triton_repo/llama3/1/model.plan

# Fire-up the Triton server
docker compose up -d triton --no-deps

# Make sure Triton server is happy
curl -s http://localhost:8000/v2/health/ready && echo " Triton is happy 🚀"

# Cleanup checkpoints (Optional)
rm -rf /data/models/triton_repo/llama3_ckpt


---
---

# Short and sweet
chmod +x ~/stacks/nv-aistack/scripts/trt_build.sh
docker compose rm -f trt-builder
docker compose up -d --force-recreate --no-deps trt-builder
docker compose logs -f trt-builder   # wait for the green tick
docker compose up -d triton --no-deps
curl -s http://localhost:8000/v2/health/ready && echo " Triton is READY 🚀"


