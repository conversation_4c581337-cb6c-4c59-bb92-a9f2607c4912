{"data": {"file-browser-filebrowser:columns": {"sizes": {"name": 139.5, "file_size": null, "is_selected": 18, "last_modified": 86.5}}, "layout-restorer:data": {"main": {"dock": {"type": "tab-area", "currentIndex": 0, "widgets": []}}, "down": {"size": 0, "widgets": []}, "left": {"collapsed": false, "visible": true, "current": "filebrowser", "widgets": ["filebrowser", "running-sessions", "@jupyterlab/toc:plugin", "extensionmanager.main-view"], "widgetStates": {"jp-running-sessions": {"sizes": [0.14285714285714285, 0.14285714285714285, 0.14285714285714285, 0.14285714285714285, 0.14285714285714285, 0.14285714285714285, 0.14285714285714285], "expansionStates": [false, false, false, false, false, false, false]}, "extensionmanager.main-view": {"sizes": [0, 1, 0], "expansionStates": [false, false, false]}}}, "right": {"collapsed": true, "visible": true, "widgets": ["jp-property-inspector", "debugger-sidebar"], "widgetStates": {"jp-debugger-sidebar": {"sizes": [0.2, 0.2, 0.2, 0.2, 0.2], "expansionStates": [false, false, false, false, false]}}}, "relativeSizes": [0.15541795665634675, 0.8445820433436533, 0], "top": {"simpleVisibility": true}}, "docmanager:recents": {"opened": [{"path": "", "contentType": "directory", "root": "/workspace"}, {"path": "README.md", "contentType": "file", "factory": "Editor", "root": "/workspace"}], "closed": [{"path": "README.md", "contentType": "file", "factory": "Editor", "root": "/workspace"}]}, "file-browser-filebrowser:cwd": {"path": ""}}, "metadata": {"id": "default"}}