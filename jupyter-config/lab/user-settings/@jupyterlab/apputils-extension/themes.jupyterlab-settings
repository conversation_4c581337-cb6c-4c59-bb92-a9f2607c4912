{
    // Theme
    // @jupyterlab/apputils-extension:themes
    // Theme manager settings.
    // *************************************

    // Theme CSS Overrides
    // Override theme CSS variables by setting key-value pairs here
    "overrides": {
        "code-font-size": "14px",
        "content-font-size1": "15px",
        "ui-font-size1": "14px"
    },

    // Selected Theme
    // Application-level visual styling theme. Ignored when Adaptive Theme is enabled.
    "theme": "JupyterLab Dark",

    // Scrollbar Theming
    // Enable/disable styling of the application scrollbars
    "theme-scrollbars": true
}