# nv-aistack Compose — Iteration 12  (builder runs external script)

networks:
  nv-aistack:
    driver: bridge

volumes:
  triton_repo:
    driver_opts:
      type: none
      o: bind
      device: /data/models/triton_repo

  notebooks:
    driver_opts:
      type: none
      o: bind
      device: /home/<USER>/stacks/nv-aistack/notebooks

  jupyter_config:
    driver_opts:
      type: none
      o: bind
      device: /home/<USER>/stacks/nv-aistack/jupyter-config

services:
  pytorch-dev:
    image: nvcr.io/nvidia/pytorch:25.04-py3
    container_name: pytorch-dev
    runtime: nvidia
    environment:
      HOSTNAME: pytorch-dev
      NVIDIA_VISIBLE_DEVICES: all
    command: >
      jupyter-lab --ip=0.0.0.0 --no-browser --NotebookApp.token=''
    ports: ["8888:8888"]
    volumes:
      - notebooks:/workspace/notebooks
      - jupyter_config:/root/.jupyter
      - triton_repo:/workspace/models
      - /data/datasets:/workspace/datasets:ro
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    healthcheck:
      test: ["CMD-SHELL", "curl -sf http://localhost:8888/api/status || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    mem_limit: 16g
    cpus: 8.00
    networks: [nv-aistack]

  trt-builder:
    image: nvcr.io/nvidia/tensorrt-llm/release:0.20.0rc4
    container_name: trt-builder
    runtime: nvidia
    ipc: host
    shm_size: 16g
    environment:
      - HOSTNAME=trt-builder
      - NVIDIA_VISIBLE_DEVICES=all
      - HUGGING_FACE_HUB_TOKEN=${HF_TOKEN:-}
    entrypoint: ["/bin/bash","-c"]
    command: |
      /scripts/trt_build.sh && echo "🟢 build done" && sleep infinity
    #command: ["sh", "-c", "while true; do sleep 1; done"]
    volumes:
      - triton_repo:/models
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - ./scripts:/scripts:ro
    healthcheck:
      test: ["CMD-SHELL", "nvidia-smi > /dev/null"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: "no"
    mem_limit: 24g
    cpus: 12.00
    networks: [nv-aistack]

  triton:
    image: nvcr.io/nvidia/tritonserver:25.05-trtllm-python-py3
    container_name: triton
    runtime: nvidia
    environment:
      HOSTNAME: triton
      NVIDIA_VISIBLE_DEVICES: all
    command: tritonserver --model-repository=/models
    depends_on:
      trt-builder:
        condition: service_completed_successfully
    ports:
      - "8000:8000"
      - "8001:8001"
      - "8002:8002"
      - "9000:9000"
    volumes:
      - triton_repo:/models
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    healthcheck:
      test: ["CMD-SHELL", "curl -sf http://localhost:8000/v2/health/ready || exit 1"]
      start_period: 60s
      interval: 30s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    mem_limit: 12g
    cpus: 6.00
    networks: [nv-aistack]

